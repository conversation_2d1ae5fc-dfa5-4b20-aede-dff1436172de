package com.geeksec.nta.graphbuilder.converter.vertex;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import com.geeksec.nta.graphbuilder.utils.crypto.HashUtils;
import org.apache.flink.streaming.api.functions.ProcessFunction;

import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;
import com.geeksec.nta.graphbuilder.model.vertex.BaseDomainVertex;
import com.geeksec.nta.graphbuilder.model.vertex.DomainVertex;

import lombok.extern.slf4j.Slf4j;

/**
 * 域名顶点转换器
 * 处理域名相关的顶点数据，将其转换为Flink Row格式并直接输出到侧输出流
 *
 * <AUTHOR>
 */
@Slf4j
public class DomainVertexConverter extends ProcessFunction<Map<String, Object>, Row> {

    private static final long serialVersionUID = 1L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) throws Exception {
        // 处理域名顶点
        processDomainVertices(vertexMap, ctx, out);

        // 处理基础域名顶点
        processBaseDomainVertices(vertexMap, ctx, out);
    }

    /**
     * 处理域名顶点
     *
     * @param vertexMap 顶点数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processDomainVertices(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) {
        // 处理域名顶点列表
        List<DomainVertex> domainVertexList = (List<DomainVertex>) vertexMap.get("domainTagList");
        if (!CollectionUtils.isEmpty(domainVertexList)) {
            for (DomainVertex domainVertex : domainVertexList) {
                Row domainVertexRow = new Row(5);
                domainVertexRow.setField(0, domainVertex.getDomainAddr());
                domainVertexRow.setField(1, domainVertex.getThreatScore());
                domainVertexRow.setField(2, domainVertex.getTrustScore());
                domainVertexRow.setField(3, domainVertex.getRemark());
                domainVertexRow.setField(4, domainVertex.getWhois());

                // 直接输出到域名顶点的侧输出流
                ctx.output(NebulaGraphOutputTag.Vertex.DOMAIN, domainVertexRow);
            }
        }

        // 处理DNS域名顶点列表
        List<DomainVertex> dnsVertexList = (List<DomainVertex>) vertexMap.get("domainVertexList");
        if (!CollectionUtils.isEmpty(dnsVertexList)) {
            for (DomainVertex domainVertex : dnsVertexList) {
                Row domainVertexRow = new Row(7);
                domainVertexRow.setField(0, domainVertex.getDomainAddr());
                domainVertexRow.setField(1, domainVertex.getThreatScore());
                domainVertexRow.setField(2, domainVertex.getTrustScore());
                domainVertexRow.setField(3, domainVertex.getRemark());
                domainVertexRow.setField(4, domainVertex.getDomainRank());
                domainVertexRow.setField(5, domainVertex.getWhois());
                domainVertexRow.setField(6, HashUtils.md5(domainVertex.getDomainAddr()));

                // 直接输出到域名顶点的侧输出流
                ctx.output(NebulaGraphOutputTag.Vertex.DOMAIN, domainVertexRow);
            }
        }

        // 处理HTTP域名顶点列表
        List<DomainVertex> httpDomainVertexList = (List<DomainVertex>) vertexMap.get("httpDomainVertexList");
        if (!CollectionUtils.isEmpty(httpDomainVertexList)) {
            for (DomainVertex domainVertex : httpDomainVertexList) {
                Row domainVertexRow = new Row(5);
                domainVertexRow.setField(0, domainVertex.getDomainAddr());
                domainVertexRow.setField(1, domainVertex.getThreatScore());
                domainVertexRow.setField(2, domainVertex.getTrustScore());
                domainVertexRow.setField(3, domainVertex.getRemark());
                domainVertexRow.setField(4, domainVertex.getWhois());

                // 直接输出到域名顶点的侧输出流
                ctx.output(NebulaGraphOutputTag.Vertex.DOMAIN, domainVertexRow);
            }
        }

        // 处理SSL域名顶点列表
        List<DomainVertex> sslDomainVertexList = (List<DomainVertex>) vertexMap.get("sslDomainVertexList");
        if (!CollectionUtils.isEmpty(sslDomainVertexList)) {
            for (DomainVertex domainVertex : sslDomainVertexList) {
                Row domainVertexRow = new Row(5);
                domainVertexRow.setField(0, domainVertex.getDomainAddr());
                domainVertexRow.setField(1, domainVertex.getThreatScore());
                domainVertexRow.setField(2, domainVertex.getTrustScore());
                domainVertexRow.setField(3, domainVertex.getRemark());
                domainVertexRow.setField(4, domainVertex.getWhois());

                // 直接输出到域名顶点的侧输出流
                ctx.output(NebulaGraphOutputTag.Vertex.DOMAIN, domainVertexRow);
            }
        }
    }

    /**
     * 处理基础域名顶点
     *
     * @param vertexMap 顶点数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processBaseDomainVertices(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) {
        // 处理基础域名顶点列表
        List<BaseDomainVertex> baseDomainVertexList = (List<BaseDomainVertex>) vertexMap.get("baseDomainTagList");
        if (!CollectionUtils.isEmpty(baseDomainVertexList)) {
            for (BaseDomainVertex baseDomainVertex : baseDomainVertexList) {
                Row baseDomainVertexRow = new Row(3);
                baseDomainVertexRow.setField(0, baseDomainVertex.getBaseDomainAddr());
                baseDomainVertexRow.setField(1, baseDomainVertex.getThreatScore());
                baseDomainVertexRow.setField(2, baseDomainVertex.getTrustScore());

                // 直接输出到基础域名顶点的侧输出流
                ctx.output(NebulaGraphOutputTag.Vertex.BASE_DOMAIN, baseDomainVertexRow);
            }
        }

        // 处理DNS基础域名顶点
        DomainVertex baseDomainVertex = (DomainVertex) vertexMap.get("baseDomainVertex");
        if (!ObjectUtils.isEmpty(baseDomainVertex)) {
            Row baseDomainVertexRow = new Row(3);
            baseDomainVertexRow.setField(0, baseDomainVertex.getDomainAddr());
            // 默认威胁和信任分数
            baseDomainVertexRow.setField(1, 0);
            baseDomainVertexRow.setField(2, 0);

            // 直接输出到基础域名顶点的侧输出流
            ctx.output(NebulaGraphOutputTag.Vertex.BASE_DOMAIN, baseDomainVertexRow);
        }
    }
}
