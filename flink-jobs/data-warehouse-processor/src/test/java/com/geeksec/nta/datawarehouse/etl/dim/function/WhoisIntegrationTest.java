package com.geeksec.nta.datawarehouse.etl.dim.function;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.geeksec.common.utils.metadata.DomainMetadataManager;

/**
 * WHOIS集成功能测试类
 * 测试域名维度表中whois信息的获取和处理
 *
 * <AUTHOR>
 */
@DisplayName("WHOIS集成功能测试")
public class WhoisIntegrationTest {

    private DomainMetadataManager domainMetadataManager;

    @BeforeEach
    public void setUp() {
        domainMetadataManager = DomainMetadataManager.getInstance();
    }

    @Test
    @DisplayName("测试获取域名WHOIS信息")
    public void testGetDomainWhois() {
        // 测试已知域名的whois信息
        String googleWhois = domainMetadataManager.getDomainWhois("google.com");
        assertNotNull(googleWhois, "google.com的whois信息不应为null");
        
        // 测试不存在的域名
        String unknownWhois = domainMetadataManager.getDomainWhois("unknown-domain-12345.com");
        assertEquals("", unknownWhois, "不存在的域名应该返回空字符串");
    }

    @Test
    @DisplayName("测试空值和无效输入处理")
    public void testNullAndInvalidInputs() {
        // 测试null输入
        String nullResult = domainMetadataManager.getDomainWhois(null);
        assertEquals("", nullResult, "null输入应该返回空字符串");

        // 测试空字符串输入
        String emptyResult = domainMetadataManager.getDomainWhois("");
        assertEquals("", emptyResult, "空字符串输入应该返回空字符串");

        // 测试空白字符串输入
        String blankResult = domainMetadataManager.getDomainWhois("   ");
        assertEquals("", blankResult, "空白字符串输入应该返回空字符串");
    }

    @Test
    @DisplayName("测试WHOIS数据缓存机制")
    public void testWhoisCaching() {
        String domain = "example.com";
        
        // 第一次调用，可能触发数据库加载
        long startTime1 = System.currentTimeMillis();
        String whois1 = domainMetadataManager.getDomainWhois(domain);
        long duration1 = System.currentTimeMillis() - startTime1;

        // 第二次调用，应该从缓存获取
        long startTime2 = System.currentTimeMillis();
        String whois2 = domainMetadataManager.getDomainWhois(domain);
        long duration2 = System.currentTimeMillis() - startTime2;

        // 验证结果一致性
        assertEquals(whois1, whois2, "两次调用应该返回相同的结果");
        
        // 缓存调用应该更快（通常情况下）
        System.out.println("第一次调用耗时: " + duration1 + "ms");
        System.out.println("第二次调用耗时: " + duration2 + "ms");
    }

    @Test
    @DisplayName("测试域名格式处理")
    public void testDomainFormatHandling() {
        // 测试不同格式的域名
        String[] testDomains = {
            "google.com",
            "www.google.com",
            "subdomain.example.org",
            "test-domain.co.uk",
            "xn--fsq.xn--0zwm56d"  // 国际化域名
        };

        for (String domain : testDomains) {
            String whois = domainMetadataManager.getDomainWhois(domain);
            assertNotNull(whois, "域名 " + domain + " 的whois查询不应返回null");
            System.out.println("域名: " + domain + ", WHOIS: " + 
                (whois.isEmpty() ? "[无数据]" : whois.substring(0, Math.min(50, whois.length()))));
        }
    }

    @Test
    @DisplayName("测试WHOIS数据刷新功能")
    public void testWhoisRefresh() {
        String domain = "test.com";
        
        // 获取初始whois信息
        String initialWhois = domainMetadataManager.getDomainWhois(domain);
        
        // 刷新缓存
        domainMetadataManager.refresh();
        
        // 再次获取whois信息
        String refreshedWhois = domainMetadataManager.getDomainWhois(domain);
        
        // 验证刷新后的结果
        assertEquals(initialWhois, refreshedWhois, "刷新前后的whois信息应该一致");
    }

    @Test
    @DisplayName("测试大量域名查询性能")
    public void testBulkWhoisQuery() {
        String[] domains = {
            "google.com", "microsoft.com", "apple.com", "amazon.com", "facebook.com",
            "twitter.com", "linkedin.com", "github.com", "stackoverflow.com", "reddit.com"
        };

        long startTime = System.currentTimeMillis();
        
        for (String domain : domains) {
            String whois = domainMetadataManager.getDomainWhois(domain);
            assertNotNull(whois, "域名 " + domain + " 的whois查询不应返回null");
        }
        
        long duration = System.currentTimeMillis() - startTime;
        System.out.println("查询 " + domains.length + " 个域名的whois信息耗时: " + duration + "ms");
        
        // 性能断言：平均每个域名查询不应超过100ms（包含首次数据库加载时间）
        assertTrue(duration / domains.length < 100, 
            "平均每个域名查询时间不应超过100ms，实际: " + (duration / domains.length) + "ms");
    }

    @Test
    @DisplayName("测试特殊字符域名处理")
    public void testSpecialCharacterDomains() {
        String[] specialDomains = {
            "test-domain.com",      // 包含连字符
            "123domain.com",        // 以数字开头
            "domain123.com",        // 以数字结尾
            "a.com",               // 单字符域名
            "very-long-domain-name-for-testing.com"  // 长域名
        };

        for (String domain : specialDomains) {
            try {
                String whois = domainMetadataManager.getDomainWhois(domain);
                assertNotNull(whois, "特殊域名 " + domain + " 的whois查询不应返回null");
            } catch (Exception e) {
                fail("处理特殊域名 " + domain + " 时不应抛出异常: " + e.getMessage());
            }
        }
    }
}
