package com.geeksec.common.utils.metadata;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.config.ConfigurationManager;

import lombok.extern.slf4j.Slf4j;

/**
 * IP WHOIS数据管理器
 * 直接从JSONL文件读取RIR Data的IP WHOIS数据
 * 提供高效的IP地址enrichment功能
 *
 * <AUTHOR>
 */
@Slf4j
public class IpWhoisManager {

    /** 单例实例 */
    private static volatile IpWhoisManager instance = null;

    /** IP网段数据列表 */
    private final List<IpRangeInfo> ipRanges = new ArrayList<>();

    /** IP查询结果缓存 */
    private final ConcurrentHashMap<String, IpWhoisInfo> queryCache = new ConcurrentHashMap<>();

    /** JSON解析器 */
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /** 数据文件路径配置键 */
    private static final String IP_WHOIS_FILE_CONFIG = "ip.whois.data.file";

    /** 默认数据文件路径 */
    private static final String DEFAULT_IP_WHOIS_FILE = "data/ip_whois_data.jsonl";

    /**
     * 私有构造函数
     */
    private IpWhoisManager() {
        initialize();
    }

    /**
     * 获取单例实例
     *
     * @return IpWhoisManager实例
     */
    public static IpWhoisManager getInstance() {
        if (instance == null) {
            synchronized (IpWhoisManager.class) {
                if (instance == null) {
                    instance = new IpWhoisManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化IP WHOIS数据
     */
    private void initialize() {
        try {
            String dataFile = ConfigurationManager.getConfig()
                .get(IP_WHOIS_FILE_CONFIG, DEFAULT_IP_WHOIS_FILE);
            
            loadIpWhoisData(dataFile);
            log.info("IP WHOIS管理器初始化成功，加载了 {} 个IP网段", ipRanges.size());
        } catch (Exception e) {
            log.error("初始化IP WHOIS管理器失败", e);
        }
    }

    /**
     * 从JSONL文件加载IP WHOIS数据
     *
     * @param jsonlFile JSONL文件路径
     */
    private void loadIpWhoisData(String jsonlFile) {
        ipRanges.clear();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(jsonlFile))) {
            String line;
            int lineNumber = 0;
            int loadedCount = 0;

            while ((line = reader.readLine()) != null) {
                lineNumber++;
                
                if (StringUtils.isEmpty(line.trim())) {
                    continue;
                }

                try {
                    IpRangeInfo rangeInfo = parseJsonLine(line);
                    if (rangeInfo != null) {
                        ipRanges.add(rangeInfo);
                        loadedCount++;
                    }
                } catch (Exception e) {
                    log.debug("解析第 {} 行失败: {}", lineNumber, e.getMessage());
                }
            }

            log.info("从 {} 加载了 {} 条IP WHOIS记录", jsonlFile, loadedCount);

        } catch (IOException e) {
            log.error("读取IP WHOIS文件失败: {}", jsonlFile, e);
        }
    }

    /**
     * 解析JSON行数据
     *
     * @param jsonLine JSON行
     * @return IpRangeInfo对象
     */
    private IpRangeInfo parseJsonLine(String jsonLine) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonLine);

            // 提取IP段信息
            String ipRange = extractIpRange(jsonNode);
            if (StringUtils.isEmpty(ipRange)) {
                return null;
            }

            // 解析IP段
            IpRange range = parseIpRange(ipRange);
            if (range == null) {
                return null;
            }

            // 提取其他信息
            String netname = getStringValue(jsonNode, "netname");
            String description = getStringValue(jsonNode, "descr");
            String country = getStringValue(jsonNode, "country");
            String status = getStringValue(jsonNode, "status");
            String maintainer = getStringValue(jsonNode, "mnt-by");

            return new IpRangeInfo(range, netname, description, country, status, maintainer);

        } catch (Exception e) {
            log.debug("解析JSON行失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取IP段信息
     */
    private String extractIpRange(JsonNode jsonNode) {
        JsonNode prefixesNode = jsonNode.get("prefixes");
        if (prefixesNode != null && prefixesNode.isArray() && prefixesNode.size() > 0) {
            return prefixesNode.get(0).asText();
        }
        return null;
    }

    /**
     * 从JSON节点获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode fieldNode = jsonNode.get(fieldName);
        if (fieldNode != null && !fieldNode.isNull()) {
            return fieldNode.asText().trim();
        }
        return "";
    }

    /**
     * 解析IP段字符串
     *
     * @param ipRangeStr IP段字符串（如 "***********/24"）
     * @return IpRange对象
     */
    private IpRange parseIpRange(String ipRangeStr) {
        try {
            if (ipRangeStr.contains("/")) {
                // CIDR格式
                String[] parts = ipRangeStr.split("/");
                if (parts.length == 2) {
                    InetAddress network = InetAddress.getByName(parts[0]);
                    int prefixLength = Integer.parseInt(parts[1]);
                    return new IpRange(network, prefixLength);
                }
            }
        } catch (Exception e) {
            log.debug("解析IP段失败: {}", ipRangeStr);
        }
        return null;
    }

    /**
     * 查询IP地址的WHOIS信息
     *
     * @param ipAddress IP地址字符串
     * @return WHOIS信息，未找到返回null
     */
    public IpWhoisInfo getIpWhoisInfo(String ipAddress) {
        if (StringUtils.isEmpty(ipAddress)) {
            return null;
        }

        // 检查缓存
        IpWhoisInfo cached = queryCache.get(ipAddress);
        if (cached != null) {
            return cached;
        }

        // 查找匹配的IP段
        try {
            InetAddress targetIp = InetAddress.getByName(ipAddress);
            
            for (IpRangeInfo rangeInfo : ipRanges) {
                if (rangeInfo.getRange().contains(targetIp)) {
                    IpWhoisInfo whoisInfo = new IpWhoisInfo(
                        rangeInfo.getNetname(),
                        rangeInfo.getDescription(),
                        rangeInfo.getCountry(),
                        rangeInfo.getStatus(),
                        rangeInfo.getMaintainer()
                    );
                    
                    // 缓存结果
                    queryCache.put(ipAddress, whoisInfo);
                    return whoisInfo;
                }
            }
        } catch (UnknownHostException e) {
            log.debug("无效的IP地址: {}", ipAddress);
        }

        return null;
    }

    /**
     * 获取IP地址的组织信息
     *
     * @param ipAddress IP地址
     * @return 组织信息
     */
    public String getIpOrganization(String ipAddress) {
        IpWhoisInfo whoisInfo = getIpWhoisInfo(ipAddress);
        if (whoisInfo != null) {
            if (StringUtils.isNotEmpty(whoisInfo.getNetname())) {
                return whoisInfo.getNetname();
            } else if (StringUtils.isNotEmpty(whoisInfo.getDescription())) {
                return whoisInfo.getDescription();
            }
        }
        return "";
    }

    /**
     * 获取IP地址的国家信息
     *
     * @param ipAddress IP地址
     * @return 国家代码
     */
    public String getIpCountry(String ipAddress) {
        IpWhoisInfo whoisInfo = getIpWhoisInfo(ipAddress);
        return whoisInfo != null ? whoisInfo.getCountry() : "";
    }

    /**
     * 刷新数据
     */
    public void refresh() {
        queryCache.clear();
        initialize();
    }

    /**
     * 获取统计信息
     */
    public String getStatistics() {
        return String.format("IP段数量: %d, 缓存大小: %d", ipRanges.size(), queryCache.size());
    }

    /**
     * IP段信息类
     */
    private static class IpRangeInfo {
        private final IpRange range;
        private final String netname;
        private final String description;
        private final String country;
        private final String status;
        private final String maintainer;

        public IpRangeInfo(IpRange range, String netname, String description,
                          String country, String status, String maintainer) {
            this.range = range;
            this.netname = netname;
            this.description = description;
            this.country = country;
            this.status = status;
            this.maintainer = maintainer;
        }

        public IpRange getRange() { return range; }
        public String getNetname() { return netname; }
        public String getDescription() { return description; }
        public String getCountry() { return country; }
        public String getStatus() { return status; }
        public String getMaintainer() { return maintainer; }
    }

    /**
     * IP段范围类
     */
    private static class IpRange {
        private final InetAddress network;
        private final int prefixLength;
        private final long networkLong;
        private final long maskLong;

        public IpRange(InetAddress network, int prefixLength) {
            this.network = network;
            this.prefixLength = prefixLength;
            this.networkLong = ipToLong(network);
            this.maskLong = (0xFFFFFFFFL << (32 - prefixLength)) & 0xFFFFFFFFL;
        }

        public boolean contains(InetAddress ip) {
            long ipLong = ipToLong(ip);
            return (ipLong & maskLong) == (networkLong & maskLong);
        }

        private long ipToLong(InetAddress ip) {
            byte[] bytes = ip.getAddress();
            if (bytes.length == 4) {
                return ((bytes[0] & 0xFFL) << 24) |
                       ((bytes[1] & 0xFFL) << 16) |
                       ((bytes[2] & 0xFFL) << 8) |
                       (bytes[3] & 0xFFL);
            }
            return 0; // IPv6 not supported in this simple implementation
        }
    }

    /**
     * IP WHOIS信息类
     */
    public static class IpWhoisInfo {
        private final String netname;
        private final String description;
        private final String country;
        private final String status;
        private final String maintainer;

        public IpWhoisInfo(String netname, String description, String country,
                          String status, String maintainer) {
            this.netname = netname;
            this.description = description;
            this.country = country;
            this.status = status;
            this.maintainer = maintainer;
        }

        public String getNetname() { return netname; }
        public String getDescription() { return description; }
        public String getCountry() { return country; }
        public String getStatus() { return status; }
        public String getMaintainer() { return maintainer; }

        public String getOrganizationSummary() {
            StringBuilder summary = new StringBuilder();
            if (StringUtils.isNotEmpty(netname)) {
                summary.append(netname);
            }
            if (StringUtils.isNotEmpty(description) && !description.equals(netname)) {
                if (summary.length() > 0) summary.append(" - ");
                summary.append(description);
            }
            return summary.toString();
        }
    }
}
