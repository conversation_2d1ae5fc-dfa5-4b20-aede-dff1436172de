package com.geeksec.common.utils.metadata;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.geeksec.common.utils.db.DatabaseConnectionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * RIR Data WHOIS数据处理器
 * 专门用于处理来自 https://rir-data.org/ 的WHOIS数据
 * 支持多种RIR Data格式的解析和导入
 *
 * <AUTHOR>
 */
@Slf4j
public class RirWhoisDataProcessor {

    /** 批量插入大小 */
    private static final int BATCH_SIZE = 1000;

    /** 数据库名称 */
    private static final String DATABASE_NAME = "nta";

    /** 插入SQL语句 */
    private static final String INSERT_SQL = 
        "INSERT INTO tb_domain_whois (domain, whois, created_time) VALUES (?, ?, ?) " +
        "ON DUPLICATE KEY UPDATE whois = VALUES(whois), created_time = VALUES(created_time)";

    /** 域名提取正则表达式 */
    private static final Pattern DOMAIN_PATTERN = Pattern.compile(
        "^([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,}$"
    );

    /** 注册商信息提取正则表达式 */
    private static final Pattern REGISTRAR_PATTERN = Pattern.compile(
        "(?i)(?:registrar|sponsoring registrar|registrar name):\\s*(.+?)(?:\\n|$)"
    );

    /** 组织信息提取正则表达式 */
    private static final Pattern ORG_PATTERN = Pattern.compile(
        "(?i)(?:registrant organization|organization|org):\\s*(.+?)(?:\\n|$)"
    );

    /**
     * 处理RIR Data格式的WHOIS数据文件
     * 支持多种常见的RIR Data WHOIS格式
     *
     * @param whoisDataFile WHOIS数据文件路径
     * @param format 数据格式类型 ("json", "txt", "csv")
     * @return 处理的记录数
     */
    public static int processRirWhoisData(String whoisDataFile, String format) {
        if (StringUtils.isEmpty(whoisDataFile)) {
            log.error("WHOIS数据文件路径不能为空");
            return 0;
        }

        log.info("开始处理RIR Data WHOIS文件: {}, 格式: {}", whoisDataFile, format);

        switch (format.toLowerCase()) {
            case "json":
                return processJsonFormat(whoisDataFile);
            case "txt":
                return processTextFormat(whoisDataFile);
            case "csv":
                return processCsvFormat(whoisDataFile);
            default:
                log.error("不支持的数据格式: {}", format);
                return 0;
        }
    }

    /**
     * 处理JSON格式的WHOIS数据
     *
     * @param jsonFile JSON文件路径
     * @return 处理的记录数
     */
    private static int processJsonFormat(String jsonFile) {
        // TODO: 实现JSON格式解析
        // 这里需要根据RIR Data的实际JSON格式进行实现
        log.info("JSON格式处理功能待实现，请先下载数据查看具体格式");
        return 0;
    }

    /**
     * 处理文本格式的WHOIS数据
     * 适用于标准WHOIS文本格式
     *
     * @param textFile 文本文件路径
     * @return 处理的记录数
     */
    private static int processTextFormat(String textFile) {
        int totalProcessed = 0;
        List<WhoisRecord> batchRecords = new ArrayList<>(BATCH_SIZE);

        try (BufferedReader reader = new BufferedReader(new FileReader(textFile))) {
            StringBuilder currentRecord = new StringBuilder();
            String line;
            int lineNumber = 0;

            while ((line = reader.readLine()) != null) {
                lineNumber++;

                // WHOIS记录通常以空行分隔
                if (StringUtils.isEmpty(line.trim())) {
                    if (currentRecord.length() > 0) {
                        WhoisRecord record = parseWhoisRecord(currentRecord.toString());
                        if (record != null) {
                            batchRecords.add(record);

                            // 批量插入
                            if (batchRecords.size() >= BATCH_SIZE) {
                                int inserted = insertBatch(batchRecords);
                                totalProcessed += inserted;
                                batchRecords.clear();
                                log.info("已处理 {} 条记录，当前行: {}", totalProcessed, lineNumber);
                            }
                        }
                        currentRecord.setLength(0);
                    }
                } else {
                    currentRecord.append(line).append("\n");
                }
            }

            // 处理最后一条记录
            if (currentRecord.length() > 0) {
                WhoisRecord record = parseWhoisRecord(currentRecord.toString());
                if (record != null) {
                    batchRecords.add(record);
                }
            }

            // 插入剩余记录
            if (!batchRecords.isEmpty()) {
                int inserted = insertBatch(batchRecords);
                totalProcessed += inserted;
            }

            log.info("文本格式WHOIS数据处理完成，总共处理 {} 条记录", totalProcessed);

        } catch (IOException e) {
            log.error("读取WHOIS文本文件失败: {}", textFile, e);
        }

        return totalProcessed;
    }

    /**
     * 处理CSV格式的WHOIS数据
     *
     * @param csvFile CSV文件路径
     * @return 处理的记录数
     */
    private static int processCsvFormat(String csvFile) {
        // 使用现有的CSV处理逻辑
        return WhoisDataImporter.importFromCsv(csvFile);
    }

    /**
     * 解析单条WHOIS记录
     *
     * @param whoisText WHOIS文本内容
     * @return WhoisRecord对象，解析失败返回null
     */
    private static WhoisRecord parseWhoisRecord(String whoisText) {
        if (StringUtils.isEmpty(whoisText)) {
            return null;
        }

        // 提取域名
        String domain = extractDomain(whoisText);
        if (StringUtils.isEmpty(domain)) {
            return null;
        }

        // 提取WHOIS信息
        String whoisInfo = extractWhoisInfo(whoisText);

        return new WhoisRecord(domain, whoisInfo);
    }

    /**
     * 从WHOIS文本中提取域名
     *
     * @param whoisText WHOIS文本
     * @return 域名，未找到返回null
     */
    private static String extractDomain(String whoisText) {
        // 查找域名字段
        String[] domainFields = {
            "Domain Name:", "domain:", "Domain:", "domain name:",
            "Query:", "query:", "Domain Query:"
        };

        for (String field : domainFields) {
            int index = whoisText.indexOf(field);
            if (index != -1) {
                String line = whoisText.substring(index).split("\n")[0];
                String domain = line.substring(field.length()).trim();
                
                // 验证域名格式
                Matcher matcher = DOMAIN_PATTERN.matcher(domain);
                if (matcher.matches()) {
                    return domain.toLowerCase();
                }
            }
        }

        return null;
    }

    /**
     * 从WHOIS文本中提取关键信息
     *
     * @param whoisText WHOIS文本
     * @return 提取的信息
     */
    private static String extractWhoisInfo(String whoisText) {
        List<String> infoParts = new ArrayList<>();

        // 提取注册商信息
        Matcher registrarMatcher = REGISTRAR_PATTERN.matcher(whoisText);
        if (registrarMatcher.find()) {
            String registrar = registrarMatcher.group(1).trim();
            if (StringUtils.isNotEmpty(registrar)) {
                infoParts.add("Registrar: " + registrar);
            }
        }

        // 提取组织信息
        Matcher orgMatcher = ORG_PATTERN.matcher(whoisText);
        if (orgMatcher.find()) {
            String org = orgMatcher.group(1).trim();
            if (StringUtils.isNotEmpty(org)) {
                infoParts.add("Organization: " + org);
            }
        }

        // 如果没有找到信息，返回默认值
        if (infoParts.isEmpty()) {
            return "WHOIS data available";
        }

        return String.join("; ", infoParts);
    }

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 插入的记录数
     */
    private static int insertBatch(List<WhoisRecord> records) {
        if (records.isEmpty()) {
            return 0;
        }

        try (Connection conn = DatabaseConnectionManager.getConnection(DATABASE_NAME);
             PreparedStatement stmt = conn.prepareStatement(INSERT_SQL)) {

            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            for (WhoisRecord record : records) {
                stmt.setString(1, record.getDomain());
                stmt.setString(2, record.getWhoisInfo());
                stmt.setString(3, currentTime);
                stmt.addBatch();
            }

            int[] results = stmt.executeBatch();
            return results.length;

        } catch (SQLException e) {
            log.error("批量插入WHOIS数据失败", e);
            return 0;
        }
    }

    /**
     * WHOIS记录数据类
     */
    private static class WhoisRecord {
        private final String domain;
        private final String whoisInfo;

        public WhoisRecord(String domain, String whoisInfo) {
            this.domain = domain;
            this.whoisInfo = whoisInfo;
        }

        public String getDomain() {
            return domain;
        }

        public String getWhoisInfo() {
            return whoisInfo;
        }
    }

    /**
     * 主方法，用于命令行执行
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        if (args.length < 2) {
            System.out.println("使用方法: java RirWhoisDataProcessor <file_path> <format>");
            System.out.println("支持的格式: json, txt, csv");
            System.out.println("");
            System.out.println("示例:");
            System.out.println("  java RirWhoisDataProcessor whois_data.txt txt");
            System.out.println("  java RirWhoisDataProcessor whois_data.json json");
            System.out.println("  java RirWhoisDataProcessor whois_data.csv csv");
            return;
        }

        String filePath = args[0];
        String format = args[1];

        log.info("开始处理RIR Data WHOIS文件: {}", filePath);
        int processed = processRirWhoisData(filePath, format);
        log.info("RIR Data WHOIS数据处理完成，共处理 {} 条记录", processed);
    }
}
