package com.geeksec.common.utils.metadata;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.utils.db.DatabaseConnectionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * IP WHOIS数据处理器
 * 专门用于处理来自RIR Data的IP地址段WHOIS数据（JSONL格式）
 * 将IP段信息导入到数据库中，用于IP地理位置和组织信息查询
 *
 * <AUTHOR>
 */
@Slf4j
public class IpWhoisDataProcessor {

    /** 批量插入大小 */
    private static final int BATCH_SIZE = 1000;

    /** 数据库名称 */
    private static final String DATABASE_NAME = "nta";

    /** IP WHOIS表插入SQL */
    private static final String INSERT_IP_WHOIS_SQL = 
        "INSERT INTO tb_ip_whois (ip_range, netname, description, country, status, maintainer, created_time) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?) " +
        "ON DUPLICATE KEY UPDATE " +
        "netname = VALUES(netname), description = VALUES(description), " +
        "country = VALUES(country), status = VALUES(status), " +
        "maintainer = VALUES(maintainer), created_time = VALUES(created_time)";

    /** JSON解析器 */
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理JSONL格式的IP WHOIS数据文件
     *
     * @param jsonlFile JSONL文件路径
     * @return 处理的记录数
     */
    public static int processIpWhoisJsonl(String jsonlFile) {
        if (StringUtils.isEmpty(jsonlFile)) {
            log.error("JSONL文件路径不能为空");
            return 0;
        }

        log.info("开始处理IP WHOIS JSONL文件: {}", jsonlFile);

        int totalProcessed = 0;
        List<IpWhoisRecord> batchRecords = new ArrayList<>(BATCH_SIZE);

        try (BufferedReader reader = new BufferedReader(new FileReader(jsonlFile))) {
            String line;
            int lineNumber = 0;

            while ((line = reader.readLine()) != null) {
                lineNumber++;

                if (StringUtils.isEmpty(line.trim())) {
                    continue; // 跳过空行
                }

                try {
                    IpWhoisRecord record = parseJsonLine(line);
                    if (record != null) {
                        batchRecords.add(record);

                        // 批量插入
                        if (batchRecords.size() >= BATCH_SIZE) {
                            int inserted = insertBatch(batchRecords);
                            totalProcessed += inserted;
                            batchRecords.clear();
                            log.info("已处理 {} 条记录，当前行: {}", totalProcessed, lineNumber);
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析第 {} 行数据失败: {}, 错误: {}", lineNumber, line, e.getMessage());
                }
            }

            // 插入剩余记录
            if (!batchRecords.isEmpty()) {
                int inserted = insertBatch(batchRecords);
                totalProcessed += inserted;
            }

            log.info("IP WHOIS数据处理完成，总共处理 {} 条记录", totalProcessed);

        } catch (IOException e) {
            log.error("读取JSONL文件失败: {}", jsonlFile, e);
        }

        return totalProcessed;
    }

    /**
     * 解析单行JSON数据
     *
     * @param jsonLine JSON行数据
     * @return IpWhoisRecord对象，解析失败返回null
     */
    private static IpWhoisRecord parseJsonLine(String jsonLine) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonLine);

            // 提取IP段信息
            String ipRange = extractIpRange(jsonNode);
            if (StringUtils.isEmpty(ipRange)) {
                return null; // 没有IP段信息，跳过
            }

            // 提取其他字段
            String netname = getStringValue(jsonNode, "netname");
            String description = getStringValue(jsonNode, "descr");
            String country = getStringValue(jsonNode, "country");
            String status = getStringValue(jsonNode, "status");
            String maintainer = getStringValue(jsonNode, "mnt-by");

            return new IpWhoisRecord(ipRange, netname, description, country, status, maintainer);

        } catch (Exception e) {
            log.debug("解析JSON行失败: {}, 错误: {}", jsonLine, e.getMessage());
            return null;
        }
    }

    /**
     * 提取IP段信息
     *
     * @param jsonNode JSON节点
     * @return IP段字符串
     */
    private static String extractIpRange(JsonNode jsonNode) {
        // 优先使用prefixes字段
        JsonNode prefixesNode = jsonNode.get("prefixes");
        if (prefixesNode != null && prefixesNode.isArray() && prefixesNode.size() > 0) {
            return prefixesNode.get(0).asText();
        }

        // 备选：使用start_address和end_address构造
        String startAddress = getStringValue(jsonNode, "start_address");
        String endAddress = getStringValue(jsonNode, "end_address");
        
        if (StringUtils.isNotEmpty(startAddress) && StringUtils.isNotEmpty(endAddress)) {
            // 如果start和end相同，可能是单个IP
            if (startAddress.equals(endAddress)) {
                return startAddress + "/32";
            } else {
                return startAddress + "-" + endAddress;
            }
        }

        return null;
    }

    /**
     * 从JSON节点获取字符串值
     *
     * @param jsonNode JSON节点
     * @param fieldName 字段名
     * @return 字符串值，不存在返回空字符串
     */
    private static String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode fieldNode = jsonNode.get(fieldName);
        if (fieldNode != null && !fieldNode.isNull()) {
            return fieldNode.asText().trim();
        }
        return "";
    }

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 插入的记录数
     */
    private static int insertBatch(List<IpWhoisRecord> records) {
        if (records.isEmpty()) {
            return 0;
        }

        try (Connection conn = DatabaseConnectionManager.getConnection(DATABASE_NAME);
             PreparedStatement stmt = conn.prepareStatement(INSERT_IP_WHOIS_SQL)) {

            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            for (IpWhoisRecord record : records) {
                stmt.setString(1, record.getIpRange());
                stmt.setString(2, record.getNetname());
                stmt.setString(3, record.getDescription());
                stmt.setString(4, record.getCountry());
                stmt.setString(5, record.getStatus());
                stmt.setString(6, record.getMaintainer());
                stmt.setString(7, currentTime);
                stmt.addBatch();
            }

            int[] results = stmt.executeBatch();
            return results.length;

        } catch (SQLException e) {
            log.error("批量插入IP WHOIS数据失败", e);
            return 0;
        }
    }

    /**
     * 创建IP WHOIS表（如果不存在）
     */
    public static void createTableIfNotExists() {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS tb_ip_whois (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                ip_range VARCHAR(50) NOT NULL COMMENT 'IP地址段',
                netname VARCHAR(100) COMMENT '网络名称',
                description VARCHAR(255) COMMENT '描述信息',
                country VARCHAR(10) COMMENT '国家代码',
                status VARCHAR(50) COMMENT '分配状态',
                maintainer VARCHAR(100) COMMENT '维护者',
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                UNIQUE KEY uk_ip_range (ip_range),
                KEY idx_country (country),
                KEY idx_netname (netname)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP地址段WHOIS信息表'
            """;

        try (Connection conn = DatabaseConnectionManager.getConnection(DATABASE_NAME);
             PreparedStatement stmt = conn.prepareStatement(createTableSql)) {
            
            stmt.executeUpdate();
            log.info("IP WHOIS表创建成功或已存在");
            
        } catch (SQLException e) {
            log.error("创建IP WHOIS表失败", e);
        }
    }

    /**
     * IP WHOIS记录数据类
     */
    private static class IpWhoisRecord {
        private final String ipRange;
        private final String netname;
        private final String description;
        private final String country;
        private final String status;
        private final String maintainer;

        public IpWhoisRecord(String ipRange, String netname, String description, 
                           String country, String status, String maintainer) {
            this.ipRange = ipRange;
            this.netname = netname;
            this.description = description;
            this.country = country;
            this.status = status;
            this.maintainer = maintainer;
        }

        public String getIpRange() { return ipRange; }
        public String getNetname() { return netname; }
        public String getDescription() { return description; }
        public String getCountry() { return country; }
        public String getStatus() { return status; }
        public String getMaintainer() { return maintainer; }
    }

    /**
     * 主方法，用于命令行执行
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("使用方法: java IpWhoisDataProcessor <jsonl_file_path>");
            System.out.println("");
            System.out.println("示例:");
            System.out.println("  java IpWhoisDataProcessor ip_whois_data.jsonl");
            System.out.println("");
            System.out.println("JSONL格式示例:");
            System.out.println("  {\"prefixes\": [\"***********/24\"], \"netname\": \"CARNET-TELE2CARNET\", \"country\": \"HR\"}");
            return;
        }

        String jsonlFile = args[0];

        // 创建表
        createTableIfNotExists();

        // 处理数据
        log.info("开始处理IP WHOIS JSONL文件: {}", jsonlFile);
        int processed = processIpWhoisJsonl(jsonlFile);
        log.info("IP WHOIS数据处理完成，共处理 {} 条记录", processed);
    }
}
