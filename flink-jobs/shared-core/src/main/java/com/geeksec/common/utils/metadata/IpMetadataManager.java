package com.geeksec.common.utils.metadata;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;

import com.geeksec.common.utils.db.DatabaseConnectionManager;
import com.geeksec.common.utils.net.NetworkUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * IP元数据管理器
 * 提供IP地址相关元数据的获取和缓存功能
 * 支持基于RIR Data的IP WHOIS信息查询
 *
 * <AUTHOR>
 */
@Slf4j
public class IpMetadataManager {

    /**
     * 单例实例
     */
    private static volatile IpMetadataManager instance = null;

    /**
     * IP WHOIS信息缓存
     * Key: IP地址, Value: WHOIS信息
     */
    private final Map<String, IpWhoisInfo> ipWhoisCache = new ConcurrentHashMap<>();

    /**
     * 私有构造函数
     */
    private IpMetadataManager() {
        initialize();
    }

    /**
     * 获取单例实例
     *
     * @return IpMetadataManager实例
     */
    public static IpMetadataManager getInstance() {
        if (instance == null) {
            synchronized (IpMetadataManager.class) {
                if (instance == null) {
                    instance = new IpMetadataManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化元数据
     */
    private void initialize() {
        try {
            log.info("IP元数据管理器初始化成功");
        } catch (Exception e) {
            log.error("初始化IP元数据管理器失败", e);
        }
    }

    /**
     * 获取IP地址的WHOIS信息
     *
     * @param ipAddress IP地址
     * @return WHOIS信息，如果不存在则返回null
     */
    public IpWhoisInfo getIpWhoisInfo(String ipAddress) {
        if (StringUtils.isEmpty(ipAddress)) {
            return null;
        }

        // 检查缓存
        IpWhoisInfo cachedInfo = ipWhoisCache.get(ipAddress);
        if (cachedInfo != null) {
            return cachedInfo;
        }

        // 从数据库查询
        IpWhoisInfo whoisInfo = queryIpWhoisFromDatabase(ipAddress);
        if (whoisInfo != null) {
            // 缓存结果
            ipWhoisCache.put(ipAddress, whoisInfo);
        }

        return whoisInfo;
    }

    /**
     * 从数据库查询IP WHOIS信息
     *
     * @param ipAddress IP地址
     * @return WHOIS信息
     */
    private IpWhoisInfo queryIpWhoisFromDatabase(String ipAddress) {
        try (Connection conn = DatabaseConnectionManager.getConnection("nta");
             Statement stmt = conn.createStatement()) {

            // 查询包含该IP的网段
            String sql = String.format(
                "SELECT ip_range, netname, description, country, status, maintainer " +
                "FROM tb_ip_whois " +
                "WHERE '%s' REGEXP REPLACE(ip_range, '/', '.*') " +
                "OR ip_range LIKE '%s%%' " +
                "LIMIT 1",
                ipAddress, ipAddress
            );

            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    return new IpWhoisInfo(
                        rs.getString("ip_range"),
                        rs.getString("netname"),
                        rs.getString("description"),
                        rs.getString("country"),
                        rs.getString("status"),
                        rs.getString("maintainer")
                    );
                }
            }

        } catch (Exception e) {
            log.error("查询IP WHOIS信息失败: {}", ipAddress, e);
        }

        return null;
    }

    /**
     * 获取IP地址的组织信息
     *
     * @param ipAddress IP地址
     * @return 组织信息字符串
     */
    public String getIpOrganization(String ipAddress) {
        IpWhoisInfo whoisInfo = getIpWhoisInfo(ipAddress);
        if (whoisInfo != null) {
            // 优先返回netname，其次是description
            if (StringUtils.isNotEmpty(whoisInfo.getNetname())) {
                return whoisInfo.getNetname();
            } else if (StringUtils.isNotEmpty(whoisInfo.getDescription())) {
                return whoisInfo.getDescription();
            }
        }
        return "";
    }

    /**
     * 获取IP地址的国家信息
     *
     * @param ipAddress IP地址
     * @return 国家代码
     */
    public String getIpCountry(String ipAddress) {
        IpWhoisInfo whoisInfo = getIpWhoisInfo(ipAddress);
        return whoisInfo != null ? whoisInfo.getCountry() : "";
    }

    /**
     * 检查IP是否为内网地址
     *
     * @param ipAddress IP地址
     * @return 是否为内网地址
     */
    public boolean isInternalIp(String ipAddress) {
        return NetworkUtils.isInternalIp(ipAddress);
    }

    /**
     * 刷新IP元数据缓存
     */
    public void refresh() {
        ipWhoisCache.clear();
        log.info("IP元数据缓存已刷新");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存大小
     */
    public int getCacheSize() {
        return ipWhoisCache.size();
    }

    /**
     * IP WHOIS信息数据类
     */
    public static class IpWhoisInfo {
        private final String ipRange;
        private final String netname;
        private final String description;
        private final String country;
        private final String status;
        private final String maintainer;

        public IpWhoisInfo(String ipRange, String netname, String description,
                          String country, String status, String maintainer) {
            this.ipRange = ipRange;
            this.netname = netname;
            this.description = description;
            this.country = country;
            this.status = status;
            this.maintainer = maintainer;
        }

        public String getIpRange() { return ipRange; }
        public String getNetname() { return netname; }
        public String getDescription() { return description; }
        public String getCountry() { return country; }
        public String getStatus() { return status; }
        public String getMaintainer() { return maintainer; }

        /**
         * 获取组织信息摘要
         *
         * @return 组织信息字符串
         */
        public String getOrganizationSummary() {
            StringBuilder summary = new StringBuilder();
            
            if (StringUtils.isNotEmpty(netname)) {
                summary.append(netname);
            }
            
            if (StringUtils.isNotEmpty(description) && !description.equals(netname)) {
                if (summary.length() > 0) {
                    summary.append(" - ");
                }
                summary.append(description);
            }
            
            if (StringUtils.isNotEmpty(country)) {
                if (summary.length() > 0) {
                    summary.append(" (").append(country).append(")");
                } else {
                    summary.append(country);
                }
            }
            
            return summary.toString();
        }

        @Override
        public String toString() {
            return String.format("IpWhoisInfo{ipRange='%s', netname='%s', description='%s', country='%s', status='%s', maintainer='%s'}",
                    ipRange, netname, description, country, status, maintainer);
        }
    }
}
