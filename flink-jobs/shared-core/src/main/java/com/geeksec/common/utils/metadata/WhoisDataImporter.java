package com.geeksec.common.utils.metadata;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.geeksec.common.utils.db.DatabaseConnectionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * WHOIS数据导入工具
 * 支持从CSV文件导入离线whois数据到数据库
 * 适用于内网环境中的whois数据集成
 *
 * <AUTHOR>
 */
@Slf4j
public class WhoisDataImporter {

    /** 批量插入大小 */
    private static final int BATCH_SIZE = 1000;

    /** 数据库名称 */
    private static final String DATABASE_NAME = "nta";

    /** 插入SQL语句 */
    private static final String INSERT_SQL = 
        "INSERT INTO tb_domain_whois (domain, whois, created_time) VALUES (?, ?, ?) " +
        "ON DUPLICATE KEY UPDATE whois = VALUES(whois), created_time = VALUES(created_time)";

    /**
     * 从CSV文件导入whois数据
     * CSV格式：domain,whois_info
     * 例如：google.com,"Google LLC"
     *
     * @param csvFilePath CSV文件路径
     * @return 导入的记录数
     */
    public static int importFromCsv(String csvFilePath) {
        if (StringUtils.isEmpty(csvFilePath)) {
            log.error("CSV文件路径不能为空");
            return 0;
        }

        int totalImported = 0;
        List<WhoisRecord> batchRecords = new ArrayList<>(BATCH_SIZE);

        try (BufferedReader reader = new BufferedReader(new FileReader(csvFilePath))) {
            String line;
            int lineNumber = 0;

            // 跳过标题行
            if ((line = reader.readLine()) != null) {
                lineNumber++;
                log.info("跳过标题行: {}", line);
            }

            while ((line = reader.readLine()) != null) {
                lineNumber++;
                
                try {
                    WhoisRecord record = parseCsvLine(line);
                    if (record != null) {
                        batchRecords.add(record);

                        // 达到批量大小时执行插入
                        if (batchRecords.size() >= BATCH_SIZE) {
                            int imported = insertBatch(batchRecords);
                            totalImported += imported;
                            batchRecords.clear();
                            log.info("已导入 {} 条记录，当前行: {}", totalImported, lineNumber);
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析第 {} 行数据失败: {}, 错误: {}", lineNumber, line, e.getMessage());
                }
            }

            // 处理剩余的记录
            if (!batchRecords.isEmpty()) {
                int imported = insertBatch(batchRecords);
                totalImported += imported;
            }

            log.info("WHOIS数据导入完成，总共导入 {} 条记录", totalImported);

        } catch (IOException e) {
            log.error("读取CSV文件失败: {}", csvFilePath, e);
        }

        return totalImported;
    }

    /**
     * 解析CSV行数据
     *
     * @param line CSV行
     * @return WhoisRecord对象，解析失败返回null
     */
    private static WhoisRecord parseCsvLine(String line) {
        if (StringUtils.isEmpty(line)) {
            return null;
        }

        // 简单的CSV解析，支持引号包围的字段
        String[] parts = parseCsvFields(line);
        if (parts.length < 2) {
            log.warn("CSV行格式不正确，字段数量不足: {}", line);
            return null;
        }

        String domain = parts[0].trim();
        String whoisInfo = parts[1].trim();

        // 验证域名格式
        if (StringUtils.isEmpty(domain) || !isValidDomain(domain)) {
            log.warn("无效的域名: {}", domain);
            return null;
        }

        return new WhoisRecord(domain, whoisInfo);
    }

    /**
     * 解析CSV字段，支持引号包围的字段
     *
     * @param line CSV行
     * @return 字段数组
     */
    private static String[] parseCsvFields(String line) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString());
                currentField.setLength(0);
            } else {
                currentField.append(c);
            }
        }
        
        // 添加最后一个字段
        fields.add(currentField.toString());
        
        return fields.toArray(new String[0]);
    }

    /**
     * 验证域名格式
     *
     * @param domain 域名
     * @return 是否为有效域名
     */
    private static boolean isValidDomain(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return false;
        }

        // 基本的域名格式验证
        return domain.matches("^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?)*$");
    }

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 插入的记录数
     */
    private static int insertBatch(List<WhoisRecord> records) {
        if (records.isEmpty()) {
            return 0;
        }

        try (Connection conn = DatabaseConnectionManager.getConnection(DATABASE_NAME);
             PreparedStatement stmt = conn.prepareStatement(INSERT_SQL)) {

            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            for (WhoisRecord record : records) {
                stmt.setString(1, record.getDomain());
                stmt.setString(2, record.getWhoisInfo());
                stmt.setString(3, currentTime);
                stmt.addBatch();
            }

            int[] results = stmt.executeBatch();
            return results.length;

        } catch (SQLException e) {
            log.error("批量插入WHOIS数据失败", e);
            return 0;
        }
    }

    /**
     * WHOIS记录数据类
     */
    private static class WhoisRecord {
        private final String domain;
        private final String whoisInfo;

        public WhoisRecord(String domain, String whoisInfo) {
            this.domain = domain;
            this.whoisInfo = whoisInfo;
        }

        public String getDomain() {
            return domain;
        }

        public String getWhoisInfo() {
            return whoisInfo;
        }
    }

    /**
     * 主方法，用于命令行执行
     *
     * @param args 命令行参数，第一个参数为CSV文件路径
     */
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("使用方法: java WhoisDataImporter <csv_file_path>");
            System.out.println("CSV格式: domain,whois_info");
            System.out.println("示例: google.com,\"Google LLC\"");
            return;
        }

        String csvFilePath = args[0];
        log.info("开始导入WHOIS数据，文件: {}", csvFilePath);
        
        int imported = importFromCsv(csvFilePath);
        log.info("WHOIS数据导入完成，共导入 {} 条记录", imported);
    }
}
