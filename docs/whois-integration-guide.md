# WHOIS数据集成指南

## 概述

本文档介绍如何在内网环境中集成离线WHOIS数据到NTA系统中。由于系统运行在内网环境，无法访问公开的WHOIS数据库，因此采用离线数据集成的方式来获取域名的WHOIS信息。

## 系统架构

### 数据流程

```
离线WHOIS数据文件 → 数据导入工具 → MySQL数据库 → DomainMetadataManager → DomainDimensionTableFunction → 维度表
```

### 核心组件

1. **WhoisDataImporter**: 离线数据导入工具
2. **DomainMetadataManager**: 域名元数据管理器，提供缓存和查询功能
3. **DomainDimensionTableFunction**: 域名维度表处理函数，集成WHOIS信息
4. **tb_domain_whois**: MySQL数据表，存储WHOIS数据

## 数据源和格式

### 支持的数据源

1. **商业数据提供商**
   - WhoisXML API离线数据包
   - DomainTools批量数据
   - Whois API历史数据导出

2. **开源数据集**
   - 公开的域名注册数据
   - 研究机构提供的WHOIS数据集
   - 历史WHOIS数据归档

3. **自建数据收集**
   - 定期收集的WHOIS查询结果
   - 从日志中提取的域名信息
   - 第三方安全厂商提供的数据

### CSV数据格式

标准的CSV格式如下：

```csv
domain,whois_info
google.com,"Google LLC"
microsoft.com,"Microsoft Corporation"
example.org,"Internet Assigned Numbers Authority"
```

**字段说明**：
- `domain`: 域名（必填）
- `whois_info`: WHOIS信息，通常包含注册机构名称（必填）

**格式要求**：
- 第一行为标题行
- 域名字段不能为空
- WHOIS信息如包含逗号需用引号包围
- 支持UTF-8编码

## 数据导入

### 使用导入脚本

```bash
# 基本导入
./scripts/whois-data-import.sh /path/to/whois_data.csv

# 导入前验证文件格式
./scripts/whois-data-import.sh --validate /path/to/whois_data.csv

# 备份现有数据后导入
./scripts/whois-data-import.sh --backup /path/to/whois_data.csv

# 清空现有数据后导入
./scripts/whois-data-import.sh --clean /path/to/whois_data.csv

# 仅验证文件，不执行导入
./scripts/whois-data-import.sh --dry-run /path/to/whois_data.csv
```

### 使用Java工具直接导入

```bash
java -cp "flink-jobs/shared-core/target/classes:flink-jobs/shared-core/target/dependency/*" \
     com.geeksec.common.utils.metadata.WhoisDataImporter \
     /path/to/whois_data.csv
```

### 批量导入示例

```bash
# 导入多个文件
for file in /data/whois/*.csv; do
    echo "导入文件: $file"
    ./scripts/whois-data-import.sh "$file"
done
```

## 数据库配置

### 表结构

```sql
CREATE TABLE `tb_domain_whois` (
  `domain` varchar(200) NOT NULL COMMENT '域名名称',
  `whois` varchar(200) DEFAULT NULL COMMENT '机构',
  `created_time` timestamp NULL DEFAULT NULL COMMENT '记录创建时间',
  PRIMARY KEY (`domain`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

### 索引优化

```sql
-- 为域名字段创建索引（如果不存在）
CREATE INDEX idx_domain ON tb_domain_whois(domain);

-- 为创建时间创建索引，便于数据管理
CREATE INDEX idx_created_time ON tb_domain_whois(created_time);
```

## 系统集成

### 在Flink作业中使用

WHOIS信息已自动集成到`DomainDimensionTableFunction`中：

```java
// 获取域名WHOIS信息（从离线数据）
String whoisInfo = domainMetadataManager.getDomainWhois(domain);
domainInfo.put("whois", whoisInfo);
```

### 缓存机制

- **内存缓存**: 使用`ConcurrentHashMap`提供高性能访问
- **延迟加载**: 首次访问时从数据库加载数据
- **缓存刷新**: 支持手动刷新缓存以获取最新数据

```java
// 刷新WHOIS数据缓存
DomainMetadataManager.getInstance().refresh();
```

## 性能优化

### 数据导入优化

1. **批量插入**: 默认批量大小为1000条记录
2. **重复处理**: 使用`ON DUPLICATE KEY UPDATE`处理重复域名
3. **事务管理**: 批量操作使用事务确保数据一致性

### 查询性能优化

1. **内存缓存**: 避免重复数据库查询
2. **索引优化**: 为域名字段建立索引
3. **连接池**: 使用数据库连接池管理连接

## 数据管理

### 数据更新策略

1. **全量更新**: 定期替换所有WHOIS数据
2. **增量更新**: 仅更新变化的域名信息
3. **版本管理**: 保留历史版本便于回滚

### 数据质量保证

1. **格式验证**: 导入前验证域名格式
2. **重复检测**: 自动处理重复域名
3. **数据清洗**: 去除无效或格式错误的记录

### 监控和维护

```sql
-- 查看WHOIS数据统计
SELECT COUNT(*) as total_domains FROM tb_domain_whois;

-- 查看最近更新的数据
SELECT domain, whois, created_time 
FROM tb_domain_whois 
ORDER BY created_time DESC 
LIMIT 10;

-- 查找空WHOIS信息的域名
SELECT domain FROM tb_domain_whois 
WHERE whois IS NULL OR whois = '';
```

## 故障排除

### 常见问题

1. **导入失败**
   - 检查CSV文件格式
   - 验证数据库连接配置
   - 查看错误日志

2. **查询性能慢**
   - 检查数据库索引
   - 监控内存使用情况
   - 考虑增加缓存大小

3. **数据不一致**
   - 刷新缓存
   - 检查数据库数据
   - 重新导入数据

### 日志分析

```bash
# 查看导入日志
grep "WHOIS" /path/to/flink/logs/*.log

# 查看错误信息
grep "ERROR.*whois" /path/to/flink/logs/*.log
```

## 最佳实践

1. **定期更新**: 建议每月更新一次WHOIS数据
2. **数据备份**: 导入前备份现有数据
3. **分批导入**: 大文件分批导入避免内存溢出
4. **监控告警**: 设置数据导入和查询的监控告警
5. **文档维护**: 记录数据来源和更新历史

## 扩展功能

### 支持更多数据格式

可以扩展导入工具支持JSON、XML等格式：

```java
// 扩展支持JSON格式
public static int importFromJson(String jsonFilePath) {
    // JSON导入实现
}
```

### 自动化数据更新

```bash
# 定时任务示例
0 2 * * 0 /path/to/scripts/whois-data-import.sh /data/weekly_whois_update.csv
```

### API接口

可以开发REST API提供WHOIS查询服务：

```java
@RestController
public class WhoisController {
    @GetMapping("/whois/{domain}")
    public String getWhois(@PathVariable String domain) {
        return DomainMetadataManager.getInstance().getDomainWhois(domain);
    }
}
```
