# IP WHOIS Enrichment 使用指南

## 概述

本指南介绍如何使用RIR Data的IP WHOIS数据为IP地址进行enrichment，提供网络组织、国家、状态等信息。

## 🎯 **数据源说明**

### RIR Data IP WHOIS数据格式

您下载的JSONL文件包含以下字段：

```json
{
  "serial": 54504831,
  "use_route": false,
  "prefixes": ["***********/24"],
  "af": 4,
  "start_address": "***********",
  "end_address": "*************",
  "netname": "CARNET-TELE2CARNET",
  "descr": "GPRS/EDGE/UMTS",
  "country": "HR",
  "status": "ASSIGNED PA",
  "mnt-by": "AS2108-MNT",
  "created": 1302176229,
  "last-modified": 1302176229,
  "source": "RIPE"
}
```

### 关键字段说明

- **prefixes**: IP地址段（CIDR格式）
- **netname**: 网络名称
- **descr**: 描述信息
- **country**: 国家代码
- **status**: 分配状态
- **mnt-by**: 维护者

## 🔧 **配置和部署**

### 1. 准备数据文件

```bash
# 1. 将下载的JSONL文件放到指定目录
mkdir -p data
cp your_downloaded_file.jsonl data/rir_ip_whois_data.jsonl

# 2. 验证文件格式
head -5 data/rir_ip_whois_data.jsonl
```

### 2. 配置文件路径

在配置文件中指定数据文件路径：

```properties
# 配置IP WHOIS数据文件路径
ip.whois.data.file=data/rir_ip_whois_data.jsonl
```

### 3. 验证配置

```bash
# 检查文件是否存在和可读
ls -la data/rir_ip_whois_data.jsonl

# 检查文件大小和行数
wc -l data/rir_ip_whois_data.jsonl
```

## 🚀 **使用方法**

### 1. 在Flink作业中自动集成

IP WHOIS enrichment已经集成到`IpDimensionTableFunction`中，会自动为IP地址添加以下信息：

- **netname**: 网络名称
- **whois_description**: WHOIS描述
- **whois_country**: WHOIS国家信息
- **whois_status**: 分配状态
- **whois_maintainer**: 维护者
- **whois_organization**: 组织摘要

### 2. 编程方式使用

```java
// 获取IP WHOIS管理器实例
IpWhoisManager whoisManager = IpWhoisManager.getInstance();

// 查询IP地址的WHOIS信息
String ipAddress = "*************";
IpWhoisManager.IpWhoisInfo whoisInfo = whoisManager.getIpWhoisInfo(ipAddress);

if (whoisInfo != null) {
    System.out.println("网络名称: " + whoisInfo.getNetname());
    System.out.println("描述: " + whoisInfo.getDescription());
    System.out.println("国家: " + whoisInfo.getCountry());
    System.out.println("状态: " + whoisInfo.getStatus());
    System.out.println("组织摘要: " + whoisInfo.getOrganizationSummary());
}

// 快速获取组织信息
String organization = whoisManager.getIpOrganization(ipAddress);
String country = whoisManager.getIpCountry(ipAddress);
```

### 3. 维度表输出示例

IP维度表会包含以下WHOIS相关字段：

```sql
SELECT 
    ip,
    netname,
    whois_description,
    whois_country,
    whois_status,
    whois_maintainer,
    whois_organization,
    country,  -- GeoIP国家信息
    city,     -- GeoIP城市信息
    asn,      -- ASN信息
    asn_org   -- ASN组织信息
FROM dim_ipv4 
WHERE ip = '*************';
```

## 📊 **数据质量和覆盖率**

### 查看统计信息

```java
// 获取统计信息
IpWhoisManager whoisManager = IpWhoisManager.getInstance();
String stats = whoisManager.getStatistics();
System.out.println(stats);
// 输出: IP段数量: 1234567, 缓存大小: 5678
```

### 数据覆盖率分析

```bash
# 分析JSONL文件中的数据分布
cat data/rir_ip_whois_data.jsonl | \
jq -r '.country' | \
sort | uniq -c | sort -nr | head -10

# 分析网络类型分布
cat data/rir_ip_whois_data.jsonl | \
jq -r '.status' | \
sort | uniq -c | sort -nr
```

## 🔍 **测试和验证**

### 1. 单元测试

```java
@Test
public void testIpWhoisEnrichment() {
    IpWhoisManager whoisManager = IpWhoisManager.getInstance();
    
    // 测试已知IP段
    String testIp = "*************";
    IpWhoisManager.IpWhoisInfo whoisInfo = whoisManager.getIpWhoisInfo(testIp);
    
    assertNotNull(whoisInfo);
    assertEquals("CARNET-TELE2CARNET", whoisInfo.getNetname());
    assertEquals("HR", whoisInfo.getCountry());
}
```

### 2. 集成测试

```bash
# 运行包含IP enrichment的Flink作业
# 检查输出的维度表数据是否包含WHOIS信息

# 查看日志中的WHOIS查询情况
grep "WHOIS" /path/to/flink/logs/*.log
```

## ⚡ **性能优化**

### 1. 内存使用优化

```properties
# 调整缓存大小
ip.whois.cache.size=50000

# 调整缓存TTL
ip.whois.cache.ttl.hours=12
```

### 2. 查询性能优化

- **预加载**: 系统启动时预加载常用IP段
- **缓存策略**: 使用LRU缓存避免内存溢出
- **批量查询**: 支持批量IP查询减少开销

### 3. 数据文件优化

```bash
# 压缩数据文件减少加载时间
gzip data/rir_ip_whois_data.jsonl

# 按IP段排序提高查询效率
sort -t'"' -k4 data/rir_ip_whois_data.jsonl > data/sorted_ip_whois_data.jsonl
```

## 🛠️ **故障排除**

### 常见问题

1. **数据文件未找到**
   ```
   错误: 读取IP WHOIS文件失败: data/rir_ip_whois_data.jsonl
   解决: 检查文件路径和权限
   ```

2. **JSON解析失败**
   ```
   错误: 解析JSON行失败
   解决: 检查JSONL文件格式，确保每行都是有效JSON
   ```

3. **IP查询无结果**
   ```
   原因: IP地址不在数据集覆盖范围内
   解决: 检查数据集的IP段覆盖情况
   ```

### 调试方法

```java
// 启用调试日志
System.setProperty("ip.whois.debug.enabled", "true");

// 查看加载的IP段数量
IpWhoisManager whoisManager = IpWhoisManager.getInstance();
System.out.println(whoisManager.getStatistics());
```

## 📈 **监控和维护**

### 1. 监控指标

- IP段加载数量
- 查询缓存命中率
- 查询响应时间
- 内存使用情况

### 2. 数据更新

```bash
# 定期更新RIR Data
# 1. 下载最新数据
wget https://rir-data.org/latest/ip_whois_data.jsonl.gz

# 2. 解压并替换
gunzip ip_whois_data.jsonl.gz
mv ip_whois_data.jsonl data/rir_ip_whois_data.jsonl

# 3. 重启Flink作业或刷新缓存
```

### 3. 性能监控

```java
// 定期输出统计信息
@Scheduled(fixedRate = 300000) // 每5分钟
public void logStatistics() {
    IpWhoisManager whoisManager = IpWhoisManager.getInstance();
    log.info("IP WHOIS统计: {}", whoisManager.getStatistics());
}
```

## 🔗 **相关资源**

- [RIR Data官网](https://rir-data.org/#whois)
- [OpenINTEL项目](https://www.openintel.nl/)
- [IP WHOIS管理器源码](../flink-jobs/shared-core/src/main/java/com/geeksec/common/utils/metadata/IpWhoisManager.java)
- [IP维度表处理函数](../flink-jobs/data-warehouse-processor/src/main/java/com/geeksec/nta/datawarehouse/etl/dim/function/IpDimensionTableFunction.java)
