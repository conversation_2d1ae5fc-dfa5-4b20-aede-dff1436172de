# WHOIS数据源获取指南

## 🚨 重要澄清：FDNS ≠ WHOIS

### Rapid7 Forward DNS (FDNS) 数据说明

**Rapid7提供的Forward DNS (FDNS)数据不是WHOIS数据**，两者完全不同：

| 数据类型 | 内容 | 示例 | 用途 |
|---------|------|------|------|
| **FDNS数据** | DNS解析记录 | `www.example.com → *************` | 网络拓扑分析、子域名发现 |
| **WHOIS数据** | 域名注册信息 | `注册商: GoDaddy, 创建时间: 2020-01-01` | 域名所有权、合规性检查 |

## 🎯 真正的WHOIS数据获取方案

### 方案一：RIR Data WHOIS数据集（强烈推荐）⭐

#### 1. **RIR Data项目 - 最佳选择**
- **机构**: OpenINTEL项目（荷兰代尔夫特理工大学）
- **网址**: https://rir-data.org/#whois
- **内容**: 高质量的域名和IP WHOIS数据集
- **获取方式**: 直接下载，无需复杂申请流程
- **数据规模**: 数百万域名的WHOIS记录
- **更新频率**: 定期更新
- **数据质量**: 学术级别，经过验证和清洗
- **格式支持**: TXT、JSON、CSV等多种格式

**使用步骤**:
```bash
# 1. 获取RIR Data信息
./scripts/download_rir_whois.sh info

# 2. 自动下载并处理数据
./scripts/download_rir_whois.sh auto

# 3. 或者分步操作
./scripts/download_rir_whois.sh download
./scripts/download_rir_whois.sh process --format txt
```

#### 2. **OpenINTEL项目（其他数据集）**
- **机构**: 荷兰代尔夫特理工大学
- **网址**: https://www.openintel.nl/
- **内容**: 大规模DNS和域名数据集
- **申请方式**: 学术申请，需要研究用途说明
- **数据规模**: 数亿域名的历史记录
- **更新频率**: 定期更新

#### 2. **WhoisDS数据集**
- **来源**: 多个学术研究项目
- **内容**: 历史WHOIS数据集
- **获取方式**: 通过学术论文引用或直接联系作者
- **规模**: 数百万域名的WHOIS记录

#### 3. **大学研究实验室**
```bash
# 可以联系的研究机构
- MIT CSAIL
- Stanford Security Lab
- UC Berkeley NetSys Lab
- CMU CyLab
- Georgia Tech IISP
```

### 方案二：公共数据和开源项目

#### 1. **ICANN数据源**
- **CZDS (Centralized Zone Data Service)**: https://czds.icann.org/
  - 内容：区域文件数据（非直接WHOIS，但包含域名列表）
  - 申请：需要ICANN账户和用途说明
  - 用途：获取域名列表后查询WHOIS

#### 2. **各国域名注册局开放数据**
```bash
# 提供开放数据的注册局
.uk (Nominet): https://www.nominet.uk/
.nl (SIDN): https://www.sidn.nl/
.se (IIS): https://www.iis.se/
.no (Norid): https://www.norid.no/
.de (DENIC): https://www.denic.de/
```

#### 3. **Kaggle数据集**
- **搜索关键词**: "whois", "domain registration", "domain analysis"
- **推荐数据集**:
  - Domain Names Dataset
  - Malicious Domain Detection Dataset
  - DNS and WHOIS Analysis Dataset

### 方案三：自建数据收集（内网适用）

#### 1. **使用系统WHOIS工具收集**

我们已经在WhoisDataImporter中集成了收集功能：

```bash
# 准备域名列表
cat > domains.txt << EOF
google.com
microsoft.com
apple.com
EOF

# 收集WHOIS数据
java -cp "target/classes:target/dependency/*" \
     com.geeksec.common.utils.metadata.WhoisDataImporter \
     collect domains.txt
```

#### 2. **使用Python脚本批量收集**

```python
#!/usr/bin/env python3
import whois
import csv
import time
import sys

def collect_whois_data(domain_file, output_file):
    """批量收集WHOIS数据"""
    with open(domain_file, 'r') as f:
        domains = [line.strip() for line in f if line.strip() and not line.startswith('#')]

    whois_data = []
    for i, domain in enumerate(domains):
        try:
            print(f"处理 {i+1}/{len(domains)}: {domain}")
            w = whois.whois(domain)

            # 提取关键信息
            registrar = str(w.registrar) if w.registrar else ""
            org = str(w.org) if hasattr(w, 'org') and w.org else ""
            whois_info = registrar or org or "Unknown"

            whois_data.append({
                'domain': domain,
                'whois_info': whois_info
            })

            # 避免被限制
            time.sleep(2)

        except Exception as e:
            print(f"错误 {domain}: {e}")
            whois_data.append({
                'domain': domain,
                'whois_info': f"Error: {str(e)}"
            })

    # 保存为CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['domain', 'whois_info'])
        writer.writeheader()
        writer.writerows(whois_data)

    print(f"完成！数据已保存到 {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("使用方法: python collect_whois.py <domain_list.txt> <output.csv>")
        sys.exit(1)

    collect_whois_data(sys.argv[1], sys.argv[2])
```

#### 3. **使用现有域名列表**

从系统日志中提取域名：
```bash
# 从DNS日志提取域名
grep -oE '[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}' /var/log/dns.log | \
sort | uniq > domains_from_logs.txt

# 从HTTP访问日志提取域名
awk '{print $7}' /var/log/apache2/access.log | \
grep -oE '[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}' | \
sort | uniq > domains_from_http.txt
```

### 方案四：商业数据服务（付费）

#### 1. **WhoisXML API**
- **网址**: https://www.whoisxmlapi.com/
- **服务**: 实时WHOIS API + 历史数据
- **价格**: 按查询次数计费
- **优势**: 数据质量高，更新及时

#### 2. **DomainTools**
- **网址**: https://www.domaintools.com/
- **服务**: 企业级域名情报
- **价格**: 订阅制
- **优势**: 丰富的域名分析功能

#### 3. **SecurityTrails**
- **网址**: https://securitytrails.com/
- **服务**: 历史DNS和WHOIS数据
- **价格**: 按API调用计费
- **优势**: 历史数据丰富

## 📋 实施建议

### 立即可行方案（1-7天）

#### **首选方案：使用RIR Data WHOIS数据集** ⭐

1. **获取RIR Data信息**:
   ```bash
   # 查看RIR Data详细信息
   ./scripts/download_rir_whois.sh info
   ```

2. **自动下载并处理RIR Data**:
   ```bash
   # 一键下载并导入RIR Data WHOIS数据
   ./scripts/download_rir_whois.sh auto
   ```

3. **手动下载RIR Data**:
   - 访问 https://rir-data.org/#whois
   - 下载最新的WHOIS数据集
   - 使用处理工具导入：
   ```bash
   java -cp "target/classes:target/dependency/*" \
        com.geeksec.common.utils.metadata.RirWhoisDataProcessor \
        downloaded_whois_data.txt txt
   ```

#### **备选方案：使用示例数据测试**

1. **导入示例数据**:
   ```bash
   # 导入我们提供的示例数据
   ./scripts/whois-data-import.sh examples/whois_sample_data.csv
   ```

2. **收集小规模WHOIS数据**:
   ```bash
   # 使用我们提供的域名列表
   java -cp "target/classes:target/dependency/*" \
        com.geeksec.common.utils.metadata.WhoisDataImporter \
        collect examples/domain_list_sample.txt
   ```

3. **从Kaggle下载现有数据集**:
   - 搜索"domain whois dataset"
   - 下载CSV格式数据
   - 使用我们的导入工具

### 中期方案（1-4周）

1. **申请学术数据集**:
   - 联系OpenINTEL项目
   - 申请ICANN CZDS访问
   - 联系相关研究机构

2. **建立自动化收集**:
   - 部署Python收集脚本
   - 设置定时任务
   - 建立数据质量监控

### 长期方案（1-3个月）

1. **商业数据订阅**:
   - 评估商业服务
   - 建立数据采购流程
   - 集成API接口

2. **数据交换合作**:
   - 与其他组织建立数据共享
   - 参与安全社区数据交换
   - 建立互惠合作关系

## ⚠️ 注意事项

### 合规性要求
- **GDPR合规**: 处理欧盟相关数据需遵守GDPR
- **使用许可**: 遵守数据提供方的使用条款
- **隐私保护**: 避免收集个人敏感信息
- **查询限制**: 遵守WHOIS服务的查询频率限制

### 技术考虑
- **查询频率**: 避免过于频繁的查询被封禁
- **数据质量**: 验证和清洗收集的数据
- **存储容量**: 大规模WHOIS数据需要足够存储空间
- **更新策略**: 建立数据更新和维护机制

## 🔗 相关资源

- [WHOIS数据集成指南](whois-integration-guide.md)
- [域名列表示例](../examples/domain_list_sample.txt)
- [WHOIS数据示例](../examples/whois_sample_data.csv)
- [数据导入脚本](../scripts/whois-data-import.sh)
