#!/usr/bin/env python3
"""
WHOIS数据收集脚本
用于从域名列表批量收集WHOIS信息并保存为CSV格式

作者: hufengkai
版本: 1.0
"""

import whois
import csv
import time
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Optional
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('whois_collection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class WhoisCollector:
    """WHOIS数据收集器"""
    
    def __init__(self, delay: float = 2.0, timeout: int = 30):
        """
        初始化收集器
        
        Args:
            delay: 查询间隔时间（秒）
            timeout: 查询超时时间（秒）
        """
        self.delay = delay
        self.timeout = timeout
        self.success_count = 0
        self.error_count = 0
    
    def is_valid_domain(self, domain: str) -> bool:
        """
        验证域名格式
        
        Args:
            domain: 域名字符串
            
        Returns:
            bool: 是否为有效域名
        """
        if not domain or len(domain) > 255:
            return False
        
        # 基本域名格式验证
        pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return bool(re.match(pattern, domain))
    
    def extract_whois_info(self, whois_data) -> str:
        """
        从WHOIS数据中提取关键信息
        
        Args:
            whois_data: python-whois返回的数据对象
            
        Returns:
            str: 提取的WHOIS信息
        """
        info_parts = []
        
        # 提取注册商信息
        if hasattr(whois_data, 'registrar') and whois_data.registrar:
            registrar = str(whois_data.registrar)
            if isinstance(whois_data.registrar, list):
                registrar = whois_data.registrar[0] if whois_data.registrar else ""
            info_parts.append(f"Registrar: {registrar}")
        
        # 提取组织信息
        if hasattr(whois_data, 'org') and whois_data.org:
            org = str(whois_data.org)
            if isinstance(whois_data.org, list):
                org = whois_data.org[0] if whois_data.org else ""
            info_parts.append(f"Organization: {org}")
        
        # 提取注册人信息
        if hasattr(whois_data, 'registrant') and whois_data.registrant:
            registrant = str(whois_data.registrant)
            info_parts.append(f"Registrant: {registrant}")
        
        # 如果没有找到任何信息，尝试其他字段
        if not info_parts:
            for attr in ['name', 'admin_name', 'tech_name']:
                if hasattr(whois_data, attr):
                    value = getattr(whois_data, attr)
                    if value:
                        if isinstance(value, list):
                            value = value[0] if value else ""
                        info_parts.append(f"{attr.replace('_', ' ').title()}: {value}")
                        break
        
        return "; ".join(info_parts) if info_parts else "No registrar info found"
    
    def query_whois(self, domain: str) -> Optional[str]:
        """
        查询单个域名的WHOIS信息
        
        Args:
            domain: 域名
            
        Returns:
            Optional[str]: WHOIS信息，失败返回None
        """
        try:
            logger.debug(f"查询域名: {domain}")
            whois_data = whois.whois(domain)
            
            if whois_data:
                whois_info = self.extract_whois_info(whois_data)
                self.success_count += 1
                logger.debug(f"成功查询 {domain}: {whois_info[:50]}...")
                return whois_info
            else:
                logger.warning(f"域名 {domain} 返回空数据")
                self.error_count += 1
                return "No data returned"
                
        except Exception as e:
            error_msg = str(e)
            logger.warning(f"查询域名 {domain} 失败: {error_msg}")
            self.error_count += 1
            return f"Error: {error_msg}"
    
    def load_domains(self, domain_file: str) -> List[str]:
        """
        从文件加载域名列表
        
        Args:
            domain_file: 域名文件路径
            
        Returns:
            List[str]: 域名列表
        """
        domains = []
        
        try:
            with open(domain_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过空行和注释行
                    if not line or line.startswith('#'):
                        continue
                    
                    # 验证域名格式
                    if self.is_valid_domain(line):
                        domains.append(line.lower())
                    else:
                        logger.warning(f"第{line_num}行域名格式无效: {line}")
            
            logger.info(f"从 {domain_file} 加载了 {len(domains)} 个有效域名")
            return domains
            
        except FileNotFoundError:
            logger.error(f"域名文件不存在: {domain_file}")
            return []
        except Exception as e:
            logger.error(f"读取域名文件失败: {e}")
            return []
    
    def collect_whois_data(self, domains: List[str]) -> List[Dict[str, str]]:
        """
        批量收集WHOIS数据
        
        Args:
            domains: 域名列表
            
        Returns:
            List[Dict[str, str]]: WHOIS数据列表
        """
        whois_data = []
        total_domains = len(domains)
        
        logger.info(f"开始收集 {total_domains} 个域名的WHOIS数据")
        
        for i, domain in enumerate(domains, 1):
            logger.info(f"处理进度: {i}/{total_domains} ({i/total_domains*100:.1f}%) - {domain}")
            
            whois_info = self.query_whois(domain)
            
            whois_data.append({
                'domain': domain,
                'whois_info': whois_info or "Query failed"
            })
            
            # 添加延迟避免被限制
            if i < total_domains:  # 最后一个域名不需要延迟
                time.sleep(self.delay)
        
        logger.info(f"收集完成！成功: {self.success_count}, 失败: {self.error_count}")
        return whois_data
    
    def save_to_csv(self, whois_data: List[Dict[str, str]], output_file: str):
        """
        保存WHOIS数据到CSV文件
        
        Args:
            whois_data: WHOIS数据列表
            output_file: 输出文件路径
        """
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['domain', 'whois_info'])
                writer.writeheader()
                writer.writerows(whois_data)
            
            logger.info(f"数据已保存到: {output_file}")
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='批量收集域名WHOIS信息',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python collect_whois.py domains.txt output.csv
  python collect_whois.py domains.txt output.csv --delay 3 --timeout 60
  python collect_whois.py domains.txt output.csv --verbose

域名文件格式:
  每行一个域名，以#开头的行为注释
  
  示例:
    # 主要网站
    google.com
    microsoft.com
    apple.com
        """
    )
    
    parser.add_argument('domain_file', help='域名列表文件路径')
    parser.add_argument('output_file', help='输出CSV文件路径')
    parser.add_argument('--delay', type=float, default=2.0, 
                       help='查询间隔时间（秒），默认2.0')
    parser.add_argument('--timeout', type=int, default=30,
                       help='查询超时时间（秒），默认30')
    parser.add_argument('--verbose', action='store_true',
                       help='显示详细日志')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 验证输入文件
    if not Path(args.domain_file).exists():
        logger.error(f"域名文件不存在: {args.domain_file}")
        sys.exit(1)
    
    # 创建输出目录
    output_path = Path(args.output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 初始化收集器
    collector = WhoisCollector(delay=args.delay, timeout=args.timeout)
    
    # 加载域名列表
    domains = collector.load_domains(args.domain_file)
    if not domains:
        logger.error("没有找到有效的域名")
        sys.exit(1)
    
    # 收集WHOIS数据
    whois_data = collector.collect_whois_data(domains)
    
    # 保存结果
    collector.save_to_csv(whois_data, args.output_file)
    
    # 显示统计信息
    logger.info(f"收集统计: 总计 {len(domains)} 个域名")
    logger.info(f"成功: {collector.success_count} 个")
    logger.info(f"失败: {collector.error_count} 个")
    logger.info(f"成功率: {collector.success_count/len(domains)*100:.1f}%")


if __name__ == "__main__":
    main()
