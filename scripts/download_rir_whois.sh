#!/bin/bash

# RIR Data WHOIS数据下载和处理脚本
# 用于从 https://rir-data.org/ 下载和处理WHOIS数据
# 
# 作者: hufengkai
# 版本: 1.0

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DATA_DIR="$PROJECT_ROOT/data/rir-whois"
JAVA_CLASS="com.geeksec.common.utils.metadata.RirWhoisDataProcessor"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示使用说明
show_usage() {
    cat << EOF
RIR Data WHOIS数据下载和处理工具

使用方法:
    $0 [选项] [操作]

操作:
    download        下载最新的RIR Data WHOIS数据
    process         处理已下载的数据
    auto            自动下载并处理数据

选项:
    -h, --help      显示此帮助信息
    -d, --data-dir  指定数据目录 (默认: $DATA_DIR)
    -f, --format    指定数据格式 (json|txt|csv, 默认: txt)
    --dry-run       仅显示将要执行的操作，不实际执行

RIR Data数据源:
    网站: https://rir-data.org/#whois
    说明: OpenINTEL项目提供的开放WHOIS数据
    更新: 定期更新

示例:
    $0 download                    # 下载数据
    $0 process                     # 处理数据
    $0 auto                        # 自动下载并处理
    $0 auto --format json          # 指定JSON格式

EOF
}

# 创建数据目录
create_data_dir() {
    if [[ ! -d "$DATA_DIR" ]]; then
        log_info "创建数据目录: $DATA_DIR"
        mkdir -p "$DATA_DIR"
    fi
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    # 检查wget或curl
    if ! command -v wget >/dev/null 2>&1 && ! command -v curl >/dev/null 2>&1; then
        missing_deps+=("wget 或 curl")
    fi
    
    # 检查Java
    if ! command -v java >/dev/null 2>&1; then
        missing_deps+=("java")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        log_error "请安装缺少的依赖后重试"
        return 1
    fi
    
    return 0
}

# 下载RIR Data WHOIS数据
download_rir_data() {
    log_info "开始下载RIR Data WHOIS数据..."
    
    create_data_dir
    
    # RIR Data的实际下载链接需要根据网站更新
    # 这里提供一个示例框架，实际使用时需要根据网站的具体下载方式调整
    
    local base_url="https://rir-data.org/data"
    local download_urls=(
        # 这些URL需要根据实际的RIR Data网站进行调整
        "$base_url/whois/latest/whois-domains.txt.gz"
        "$base_url/whois/latest/whois-domains.json.gz"
    )
    
    log_warn "注意: RIR Data的下载链接可能需要手动获取"
    log_warn "请访问 https://rir-data.org/#whois 获取最新的下载链接"
    
    # 示例下载逻辑
    for url in "${download_urls[@]}"; do
        local filename=$(basename "$url")
        local output_file="$DATA_DIR/$filename"
        
        log_info "下载: $url"
        
        if command -v wget >/dev/null 2>&1; then
            if wget -O "$output_file" "$url" 2>/dev/null; then
                log_success "下载完成: $filename"
                
                # 解压缩
                if [[ "$filename" == *.gz ]]; then
                    log_info "解压缩: $filename"
                    gunzip "$output_file" || log_warn "解压缩失败: $filename"
                fi
            else
                log_warn "下载失败: $url (可能需要手动下载)"
            fi
        elif command -v curl >/dev/null 2>&1; then
            if curl -L -o "$output_file" "$url" 2>/dev/null; then
                log_success "下载完成: $filename"
                
                # 解压缩
                if [[ "$filename" == *.gz ]]; then
                    log_info "解压缩: $filename"
                    gunzip "$output_file" || log_warn "解压缩失败: $filename"
                fi
            else
                log_warn "下载失败: $url (可能需要手动下载)"
            fi
        fi
    done
    
    # 显示下载的文件
    log_info "数据目录内容:"
    ls -la "$DATA_DIR"
}

# 处理WHOIS数据
process_whois_data() {
    local format="${1:-txt}"
    
    log_info "开始处理WHOIS数据，格式: $format"
    
    if [[ ! -d "$DATA_DIR" ]]; then
        log_error "数据目录不存在: $DATA_DIR"
        log_error "请先运行下载操作"
        return 1
    fi
    
    # 查找数据文件
    local data_files=()
    case "$format" in
        "txt")
            mapfile -t data_files < <(find "$DATA_DIR" -name "*.txt" -type f)
            ;;
        "json")
            mapfile -t data_files < <(find "$DATA_DIR" -name "*.json" -type f)
            ;;
        "csv")
            mapfile -t data_files < <(find "$DATA_DIR" -name "*.csv" -type f)
            ;;
        *)
            log_error "不支持的格式: $format"
            return 1
            ;;
    esac
    
    if [[ ${#data_files[@]} -eq 0 ]]; then
        log_error "未找到 $format 格式的数据文件"
        log_info "数据目录内容:"
        ls -la "$DATA_DIR"
        return 1
    fi
    
    # 构建classpath
    local classpath="$PROJECT_ROOT/flink-jobs/shared-core/target/classes"
    classpath="$classpath:$PROJECT_ROOT/flink-jobs/shared-core/target/dependency/*"
    
    # 处理每个数据文件
    for data_file in "${data_files[@]}"; do
        log_info "处理文件: $(basename "$data_file")"
        
        if java -cp "$classpath" "$JAVA_CLASS" "$data_file" "$format"; then
            log_success "文件处理完成: $(basename "$data_file")"
        else
            log_error "文件处理失败: $(basename "$data_file")"
        fi
    done
}

# 自动下载并处理
auto_process() {
    local format="${1:-txt}"
    
    log_info "开始自动下载并处理RIR Data WHOIS数据"
    
    # 下载数据
    download_rir_data
    
    # 处理数据
    process_whois_data "$format"
    
    log_success "自动处理完成"
}

# 显示RIR Data信息
show_rir_info() {
    cat << EOF
${BLUE}RIR Data WHOIS数据源信息${NC}

网站: https://rir-data.org/#whois
提供方: OpenINTEL项目 (荷兰代尔夫特理工大学)
数据类型: 域名WHOIS记录、IP WHOIS记录
更新频率: 定期更新
数据质量: 高质量学术数据
使用许可: 学术和研究用途

数据获取步骤:
1. 访问 https://rir-data.org/#whois
2. 注册账户（如需要）
3. 下载最新的WHOIS数据集
4. 使用本脚本处理数据

支持的数据格式:
- TXT: 标准WHOIS文本格式
- JSON: 结构化JSON格式
- CSV: 逗号分隔值格式

EOF
}

# 主函数
main() {
    local operation=""
    local format="txt"
    local dry_run=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -d|--data-dir)
                DATA_DIR="$2"
                shift 2
                ;;
            -f|--format)
                format="$2"
                shift 2
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            download|process|auto|info)
                operation="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 检查操作参数
    if [[ -z "$operation" ]]; then
        log_error "请指定操作: download, process, auto, 或 info"
        show_usage
        exit 1
    fi
    
    # 显示配置信息
    log_info "数据目录: $DATA_DIR"
    log_info "数据格式: $format"
    
    if [[ "$dry_run" == true ]]; then
        log_warn "DRY RUN模式 - 仅显示操作，不实际执行"
        exit 0
    fi
    
    # 检查依赖
    if [[ "$operation" != "info" ]]; then
        if ! check_dependencies; then
            exit 1
        fi
    fi
    
    # 执行操作
    case "$operation" in
        "download")
            download_rir_data
            ;;
        "process")
            process_whois_data "$format"
            ;;
        "auto")
            auto_process "$format"
            ;;
        "info")
            show_rir_info
            ;;
        *)
            log_error "未知操作: $operation"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
