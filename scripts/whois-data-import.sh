#!/bin/bash

# WHOIS数据导入脚本
# 用于在内网环境中导入离线whois数据到数据库
# 
# 作者: hufengkai
# 版本: 1.0

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
JAVA_CLASS="com.geeksec.common.utils.metadata.WhoisDataImporter"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示使用说明
show_usage() {
    cat << EOF
WHOIS数据导入工具

使用方法:
    $0 [选项] <CSV文件路径>

选项:
    -h, --help          显示此帮助信息
    -v, --validate      导入前验证CSV文件格式
    -b, --backup        导入前备份现有数据
    -c, --clean         导入前清空现有whois数据
    --dry-run          仅验证文件，不执行实际导入

CSV文件格式:
    domain,whois_info
    google.com,"Google LLC"
    microsoft.com,"Microsoft Corporation"

示例:
    $0 /path/to/whois_data.csv
    $0 --validate --backup /path/to/whois_data.csv
    $0 --clean /path/to/whois_data.csv

EOF
}

# 验证CSV文件格式
validate_csv() {
    local csv_file="$1"
    
    log_info "验证CSV文件格式: $csv_file"
    
    if [[ ! -f "$csv_file" ]]; then
        log_error "文件不存在: $csv_file"
        return 1
    fi
    
    # 检查文件是否为空
    if [[ ! -s "$csv_file" ]]; then
        log_error "文件为空: $csv_file"
        return 1
    fi
    
    # 检查第一行是否为标题行
    local first_line=$(head -n 1 "$csv_file")
    if [[ "$first_line" != *"domain"* ]] || [[ "$first_line" != *"whois"* ]]; then
        log_warn "CSV文件可能缺少标题行，期望格式: domain,whois_info"
    fi
    
    # 统计行数
    local line_count=$(wc -l < "$csv_file")
    log_info "CSV文件包含 $line_count 行数据"
    
    # 检查前几行的格式
    log_info "检查前5行数据格式..."
    head -n 5 "$csv_file" | while IFS= read -r line; do
        if [[ -n "$line" ]]; then
            local field_count=$(echo "$line" | tr ',' '\n' | wc -l)
            if [[ $field_count -lt 2 ]]; then
                log_warn "行格式可能不正确: $line"
            fi
        fi
    done
    
    log_success "CSV文件格式验证完成"
    return 0
}

# 备份现有数据
backup_existing_data() {
    log_info "备份现有whois数据..."
    
    local backup_file="/tmp/whois_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # 这里需要根据实际的数据库配置调整
    # mysqldump -h $MYSQL_HOST -u $MYSQL_USER -p$MYSQL_PASSWORD nta tb_domain_whois > "$backup_file"
    
    log_info "数据备份已保存到: $backup_file"
    log_warn "请根据实际环境配置数据库连接参数"
}

# 清空现有数据
clean_existing_data() {
    log_warn "清空现有whois数据..."
    log_warn "此操作将删除tb_domain_whois表中的所有数据"
    
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        return 1
    fi
    
    # 这里需要根据实际的数据库配置调整
    # mysql -h $MYSQL_HOST -u $MYSQL_USER -p$MYSQL_PASSWORD nta -e "TRUNCATE TABLE tb_domain_whois;"
    
    log_success "现有数据已清空"
    log_warn "请根据实际环境配置数据库连接参数"
}

# 执行数据导入
import_data() {
    local csv_file="$1"
    
    log_info "开始导入whois数据: $csv_file"
    
    # 构建classpath
    local classpath="$PROJECT_ROOT/flink-jobs/shared-core/target/classes"
    classpath="$classpath:$PROJECT_ROOT/flink-jobs/shared-core/target/dependency/*"
    
    # 执行Java导入程序
    if java -cp "$classpath" "$JAVA_CLASS" "$csv_file"; then
        log_success "WHOIS数据导入完成"
        return 0
    else
        log_error "WHOIS数据导入失败"
        return 1
    fi
}

# 主函数
main() {
    local csv_file=""
    local validate_only=false
    local backup_data=false
    local clean_data=false
    local dry_run=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -v|--validate)
                validate_only=true
                shift
                ;;
            -b|--backup)
                backup_data=true
                shift
                ;;
            -c|--clean)
                clean_data=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$csv_file" ]]; then
                    csv_file="$1"
                else
                    log_error "只能指定一个CSV文件"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必需参数
    if [[ -z "$csv_file" ]]; then
        log_error "请指定CSV文件路径"
        show_usage
        exit 1
    fi
    
    # 验证CSV文件
    if ! validate_csv "$csv_file"; then
        exit 1
    fi
    
    # 如果只是验证模式，则退出
    if [[ "$validate_only" == true ]] || [[ "$dry_run" == true ]]; then
        log_success "文件验证完成，未执行实际导入"
        exit 0
    fi
    
    # 备份现有数据
    if [[ "$backup_data" == true ]]; then
        backup_existing_data
    fi
    
    # 清空现有数据
    if [[ "$clean_data" == true ]]; then
        if ! clean_existing_data; then
            exit 1
        fi
    fi
    
    # 执行导入
    if ! import_data "$csv_file"; then
        exit 1
    fi
    
    log_success "所有操作完成"
}

# 执行主函数
main "$@"
