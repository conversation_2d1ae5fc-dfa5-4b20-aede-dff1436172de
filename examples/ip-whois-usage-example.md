# IP WHOIS Enrichment 使用示例

## 🎯 **快速开始**

### 1. 准备RIR Data JSONL文件

```bash
# 将您下载的RIR Data文件放到项目目录
mkdir -p data
cp your_rir_whois_data.jsonl data/rir_ip_whois_data.jsonl

# 验证文件格式
head -3 data/rir_ip_whois_data.jsonl
```

### 2. 配置文件路径

在配置文件中添加：
```properties
ip.whois.data.file=data/rir_ip_whois_data.jsonl
```

### 3. 运行Flink作业

IP WHOIS enrichment会自动集成到IP维度表处理中，无需额外配置。

## 📊 **数据示例**

### 输入数据（RIR Data JSONL）
```json
{
  "serial": 54504831,
  "prefixes": ["***********/24"],
  "netname": "CARNET-TELE2CARNET",
  "descr": "GPRS/EDGE/UMTS",
  "country": "HR",
  "status": "ASSIGNED PA",
  "mnt-by": "AS2108-MNT"
}
```

### 输出数据（IP维度表）
```sql
SELECT 
    ip,
    city,                    -- GeoIP城市
    country,                 -- GeoIP国家
    netname,                 -- WHOIS网络名称
    whois_description,       -- WHOIS描述
    whois_country,           -- WHOIS国家
    whois_status,            -- WHOIS状态
    whois_organization       -- WHOIS组织摘要
FROM dim_ipv4 
WHERE ip = '*************';
```

可能的输出：
```
ip: *************
city: Zagreb
country: Croatia
netname: CARNET-TELE2CARNET
whois_description: GPRS/EDGE/UMTS
whois_country: HR
whois_status: ASSIGNED PA
whois_organization: CARNET-TELE2CARNET - GPRS/EDGE/UMTS (HR)
```

## 🔧 **编程接口**

### 直接使用IpWhoisManager

```java
// 获取管理器实例
IpWhoisManager whoisManager = IpWhoisManager.getInstance();

// 查询IP的WHOIS信息
String ip = "*************";
IpWhoisManager.IpWhoisInfo whoisInfo = whoisManager.getIpWhoisInfo(ip);

if (whoisInfo != null) {
    System.out.println("网络名称: " + whoisInfo.getNetname());
    System.out.println("描述: " + whoisInfo.getDescription());
    System.out.println("国家: " + whoisInfo.getCountry());
    System.out.println("状态: " + whoisInfo.getStatus());
    System.out.println("维护者: " + whoisInfo.getMaintainer());
    System.out.println("组织摘要: " + whoisInfo.getOrganizationSummary());
}

// 快速获取组织和国家信息
String organization = whoisManager.getIpOrganization(ip);
String country = whoisManager.getIpCountry(ip);
```

## 📈 **性能特点**

- **内存加载**: 启动时一次性加载所有IP段到内存
- **快速查询**: 使用IP段匹配算法，查询时间复杂度O(n)
- **智能缓存**: 查询结果缓存，避免重复计算
- **低延迟**: 典型查询时间 < 1ms

## 🔍 **监控和调试**

### 查看统计信息
```java
IpWhoisManager whoisManager = IpWhoisManager.getInstance();
System.out.println(whoisManager.getStatistics());
// 输出: IP段数量: 1234567, 缓存大小: 5678
```

### 调试日志
```properties
# 启用调试日志
ip.whois.debug.enabled=true
ip.whois.log.level=DEBUG
```

## ⚠️ **注意事项**

1. **内存使用**: 大型JSONL文件会占用较多内存
2. **启动时间**: 首次加载可能需要几秒到几分钟
3. **数据覆盖**: 只能查询JSONL文件中包含的IP段
4. **IPv6支持**: 当前实现主要针对IPv4，IPv6支持有限

## 🛠️ **故障排除**

### 常见问题

1. **文件未找到**
   ```
   错误: 读取IP WHOIS文件失败
   解决: 检查文件路径配置和文件权限
   ```

2. **查询无结果**
   ```
   原因: IP不在数据集覆盖范围内
   解决: 检查JSONL文件的IP段覆盖情况
   ```

3. **内存不足**
   ```
   原因: JSONL文件过大
   解决: 增加JVM内存或分割数据文件
   ```

### 验证数据加载
```bash
# 检查JSONL文件格式
jq . data/rir_ip_whois_data.jsonl | head -10

# 统计IP段数量
grep -c "prefixes" data/rir_ip_whois_data.jsonl

# 查看国家分布
jq -r '.country' data/rir_ip_whois_data.jsonl | sort | uniq -c | sort -nr | head -10
```

## 📚 **相关文档**

- [IP WHOIS Enrichment 详细指南](ip-whois-enrichment-guide.md)
- [RIR Data官网](https://rir-data.org/#whois)
- [IpWhoisManager源码](../flink-jobs/shared-core/src/main/java/com/geeksec/common/utils/metadata/IpWhoisManager.java)
